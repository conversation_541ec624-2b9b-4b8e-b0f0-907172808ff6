# 作品管理功能说明

## 功能概述

作品管理功能为竞赛管理系统提供了完整的作品生命周期管理，包括作品创建、编辑、提交、审核等功能。

## 主要功能

### 1. 作品信息管理
- **作品基本信息**：作品名称、类型、摘要等
- **项目计划书**：支持上传项目计划书文件
- **视频材料**：支持网盘链接和提取码
- **作品状态**：草稿、已提交、审核中、审核通过、审核不通过

### 2. 参与者管理
- **参赛人员**：支持添加多个参赛人员
- **角色管理**：队长、队员角色区分
- **学生信息**：姓名、学号、联系方式等

### 3. 指导老师管理
- **指导教师**：支持添加多个指导老师
- **教师信息**：姓名、单位、联系方式等
- **角色类型**：指导老师、顾问等

### 4. 文件管理
- **文件上传**：支持多种格式文件上传
- **文件下载**：支持作品材料下载
- **文件预览**：支持常见格式文件预览

## 技术实现

### 后端实现

#### 1. 实体类设计
```java
@Data
@TableName(value = "work", autoResultMap = true)
public class Work implements Serializable {
    private String id;              // 主键
    private String code;            // 作品编号
    private String name;            // 作品名称
    private String competitionCode; // 赛事编号
    private String type;            // 作品类型
    private String abstract;        // 作品摘要
    private Integer status;         // 作品状态
    private List<WorkParticipant> participants;  // 参与者信息
    private List<WorkInstructor> instructors;    // 指导老师信息
    private List<WorkFile> files;               // 作品文件列表
    // ... 其他字段
}
```

#### 2. 核心接口
- `WorkController`: REST API控制器
- `WorkService`: 业务逻辑服务
- `WorkMapper`: 数据访问层

#### 3. 主要API接口
```
GET    /api/competition/work/user/{userId}     - 获取用户作品列表
GET    /api/competition/work/detail/{id}       - 获取作品详情
POST   /api/competition/work/add               - 新增作品
PUT    /api/competition/work/update/{id}       - 更新作品
PUT    /api/competition/work/info/{id}         - 更新作品基本信息
PUT    /api/competition/work/participants/{id} - 更新参与者信息
PUT    /api/competition/work/instructors/{id}  - 更新指导老师信息
POST   /api/competition/work/submit/{id}       - 提交作品
DELETE /api/competition/work/delete/{id}       - 删除作品
```

### 前端实现

#### 1. 页面结构
- **作品列表页面**：显示用户的所有作品
- **作品详情弹窗**：查看作品完整信息
- **编辑功能弹窗**：分模块编辑作品信息

#### 2. 主要组件
- `CompetitionWork.vue`: 作品管理主页面
- 详情弹窗：展示作品完整信息
- 编辑弹窗：分别编辑参赛人员、作品信息、指导老师

#### 3. 功能特性
- **响应式设计**：适配不同屏幕尺寸
- **实时验证**：表单数据实时验证
- **文件上传**：支持拖拽上传
- **状态管理**：作品状态可视化

## 数据库设计

### work表结构
```sql
CREATE TABLE `work` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `code` varchar(64) NOT NULL COMMENT '作品编号',
    `name` varchar(128) NOT NULL COMMENT '作品名称',
    `competition_code` varchar(32) NOT NULL COMMENT '赛事编号',
    `type` varchar(32) DEFAULT NULL COMMENT '作品类型',
    `abstract` text COMMENT '作品摘要',
    `project_plan` varchar(255) DEFAULT NULL COMMENT '项目计划书路径',
    `video_link` varchar(255) DEFAULT NULL COMMENT '视频链接',
    `video_code` varchar(32) DEFAULT NULL COMMENT '视频提取码',
    `status` int(11) DEFAULT '0' COMMENT '作品状态',
    `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
    `participants` longtext COMMENT '参与者信息（JSON格式）',
    `instructors` longtext COMMENT '指导老师信息（JSON格式）',
    `files` longtext COMMENT '作品文件列表（JSON格式）',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_user_id` int(11) DEFAULT NULL,
    `update_user_id` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_competition_code` (`competition_code`),
    KEY `idx_create_user_id` (`create_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 使用说明

### 1. 查看作品列表
- 登录系统后进入"作品管理"页面
- 查看已报名赛事的作品信息
- 支持按状态、类型等筛选

### 2. 查看作品详情
- 点击"查看详情"按钮
- 查看作品完整信息，包括基本信息、参与者、指导老师、文件等
- 支持直接下载作品材料

### 3. 编辑作品信息
- 点击"编辑作品"按钮
- 分模块编辑：参赛人员、作品信息、指导老师
- 实时保存，支持撤销操作

### 4. 提交作品
- 完善作品信息后点击"提交作品"
- 提交后作品状态变为"已提交"
- 提交后不可再修改

### 5. 文件管理
- 支持上传项目计划书、演示视频等
- 支持多种文件格式
- 提供文件下载功能

## 权限控制

- **作品创建**：只有报名用户可以创建作品
- **作品编辑**：只有作品创建者可以编辑
- **作品查看**：创建者和管理员可以查看
- **作品审核**：只有管理员可以审核

## 状态流转

```
草稿(0) -> 已提交(1) -> 审核中(2) -> 审核通过(3)/审核不通过(4)
```

- **草稿**：可以编辑和删除
- **已提交**：不可编辑，等待审核
- **审核中**：管理员审核中
- **审核通过**：作品通过审核
- **审核不通过**：作品未通过审核，可重新修改

## 注意事项

1. **文件大小限制**：单个文件不超过50MB
2. **文件格式限制**：支持PDF、Word、PPT、ZIP、RAR等格式
3. **提交时间**：注意赛事截止时间，逾期无法提交
4. **信息完整性**：提交前确保所有必要信息已填写完整
5. **备份重要**：建议本地保留作品材料备份
