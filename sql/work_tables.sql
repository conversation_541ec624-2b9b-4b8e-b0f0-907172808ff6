-- 作品表
DROP TABLE IF EXISTS `work`;
CREATE TABLE `work` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `code` varchar(64) NOT NULL COMMENT '作品编号',
  `name` varchar(128) NOT NULL COMMENT '作品名称',
  `competition_code` varchar(32) NOT NULL COMMENT '赛事编号',
  `type` varchar(32) DEFAULT NULL COMMENT '作品类型',
  `abstract` text COMMENT '作品简介',
  `project_plan_path` varchar(500) DEFAULT NULL COMMENT '项目计划书文件路径',
  `project_plan_name` varchar(128) DEFAULT NULL COMMENT '项目计划书文件名',
  `video_link` varchar(500) DEFAULT NULL COMMENT '视频链接',
  `video_code` varchar(20) DEFAULT NULL COMMENT '视频提取码',
  `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '作品状态：draft-草稿，submitted-已提交，reviewed-已审核',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `user_id` int DEFAULT NULL COMMENT '创建用户ID',
  `team_id` varchar(32) DEFAULT NULL COMMENT '团队ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_competition_code` (`competition_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='作品表';

-- 作品参与者表
DROP TABLE IF EXISTS `work_participant`;
CREATE TABLE `work_participant` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `work_id` varchar(32) NOT NULL COMMENT '作品ID',
  `name` varchar(32) NOT NULL COMMENT '姓名',
  `role` varchar(20) DEFAULT 'participant' COMMENT '角色：leader-队长，participant-队员',
  `student_id` varchar(32) DEFAULT NULL COMMENT '学号',
  `phone` varchar(16) DEFAULT NULL COMMENT '手机号',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_work_id` (`work_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='作品参与者表';

-- 作品指导老师表
DROP TABLE IF EXISTS `work_instructor`;
CREATE TABLE `work_instructor` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `work_id` varchar(32) NOT NULL COMMENT '作品ID',
  `name` varchar(32) NOT NULL COMMENT '姓名',
  `role` varchar(20) DEFAULT 'instructor' COMMENT '角色：instructor-指导老师，advisor-顾问',
  `unit` varchar(128) DEFAULT NULL COMMENT '单位',
  `contact` varchar(64) DEFAULT NULL COMMENT '联系方式',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_work_id` (`work_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='作品指导老师表';

-- 作品文件表
DROP TABLE IF EXISTS `work_file`;
CREATE TABLE `work_file` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `work_id` varchar(32) NOT NULL COMMENT '作品ID',
  `name` varchar(128) NOT NULL COMMENT '文件名',
  `path` varchar(500) NOT NULL COMMENT '文件路径',
  `url` varchar(500) DEFAULT NULL COMMENT '访问URL',
  `type` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `upload_time` datetime NOT NULL COMMENT '上传时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_work_id` (`work_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='作品文件表';

-- 插入测试数据
INSERT INTO `work` (`id`, `code`, `name`, `competition_code`, `type`, `abstract`, `status`, `user_id`, `create_time`, `update_time`) VALUES
('work001', 'WORK-2024-001', '国际大学生程序设计竞赛作品', 'COMP-2024-001', '个人赛', '这是一个关于算法优化的作品，主要研究图论算法在实际问题中的应用。', 'draft', 1, NOW(), NOW()),
('work002', 'WORK-2024-002', '创新创业大赛项目', 'COMP-2024-002', '团队赛', '基于人工智能的智能推荐系统，旨在为用户提供个性化的服务体验。', 'submitted', 2, NOW(), NOW());

INSERT INTO `work_participant` (`id`, `work_id`, `name`, `role`, `student_id`, `phone`, `email`, `create_time`, `update_time`) VALUES
('part001', 'work001', '张三', 'leader', '2021001', '13800138001', '<EMAIL>', NOW(), NOW()),
('part002', 'work002', '李四', 'leader', '2021002', '13800138002', '<EMAIL>', NOW(), NOW()),
('part003', 'work002', '王五', 'participant', '2021003', '13800138003', '<EMAIL>', NOW(), NOW());

INSERT INTO `work_instructor` (`id`, `work_id`, `name`, `role`, `unit`, `contact`, `create_time`, `update_time`) VALUES
('inst001', 'work001', '赵老师', 'instructor', '计算机学院', '<EMAIL>', NOW(), NOW()),
('inst002', 'work002', '钱老师', 'instructor', '软件学院', '<EMAIL>', NOW(), NOW()),
('inst003', 'work002', '孙老师', 'advisor', '创新创业学院', '<EMAIL>', NOW(), NOW());
