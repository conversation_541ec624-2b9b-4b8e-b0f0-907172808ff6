-- 作品表
CREATE TABLE IF NOT EXISTS `work` (
    `id` varchar(32) NOT NULL COMMENT '主键',
    `code` varchar(64) NOT NULL COMMENT '作品编号',
    `name` varchar(128) NOT NULL COMMENT '作品名称',
    `competition_code` varchar(32) NOT NULL COMMENT '赛事编号',
    `type` varchar(32) DEFAULT NULL COMMENT '作品类型',
    `abstract` text COMMENT '作品摘要',
    `project_plan` varchar(255) DEFAULT NULL COMMENT '项目计划书路径',
    `video_link` varchar(255) DEFAULT NULL COMMENT '视频链接',
    `video_code` varchar(32) DEFAULT NULL COMMENT '视频提取码',
    `status` int(11) DEFAULT '0' COMMENT '作品状态：0-草稿，1-已提交，2-审核中，3-审核通过，4-审核不通过',
    `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
    `participants` longtext COMMENT '参与者信息（JSON格式）',
    `instructors` longtext COMMENT '指导老师信息（JSON格式）',
    `files` longtext COMMENT '作品文件列表（JSON格式）',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user_id` int(11) DEFAULT NULL COMMENT '创建用户ID',
    `update_user_id` int(11) DEFAULT NULL COMMENT '更新用户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_competition_code` (`competition_code`),
    KEY `idx_create_user_id` (`create_user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='作品表';
