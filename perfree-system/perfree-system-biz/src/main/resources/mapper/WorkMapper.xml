<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.perfree.mapper.WorkMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.perfree.model.Work">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="competition_code" property="competitionCode" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="abstract" property="abstract" jdbcType="LONGVARCHAR"/>
        <result column="project_plan" property="projectPlan" jdbcType="VARCHAR"/>
        <result column="video_link" property="videoLink" jdbcType="VARCHAR"/>
        <result column="video_code" property="videoCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="submit_time" property="submitTime" jdbcType="TIMESTAMP"/>
        <result column="participants" property="participants" jdbcType="LONGVARCHAR" 
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="instructors" property="instructors" jdbcType="LONGVARCHAR" 
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="files" property="files" jdbcType="LONGVARCHAR" 
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user_id" property="createUserId" jdbcType="INTEGER"/>
        <result column="update_user_id" property="updateUserId" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, code, name, competition_code, type, abstract, project_plan, video_link, video_code,
        status, submit_time, participants, instructors, files, create_time, update_time, 
        create_user_id, update_user_id
    </sql>

    <!-- 分页查询作品列表 -->
    <select id="selectWorkPage" resultMap="BaseResultMap" parameterType="com.perfree.controller.competition.work.vo.WorkPageReqVO">
        SELECT <include refid="Base_Column_List"/>
        FROM work
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="competitionCode != null and competitionCode != ''">
                AND competition_code = #{competitionCode}
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="createUserId != null">
                AND create_user_id = #{createUserId}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT 100
    </select>

    <!-- 根据用户ID查询作品列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM work
        WHERE create_user_id = #{userId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据赛事编号查询作品列表 -->
    <select id="selectByCompetitionCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM work
        WHERE competition_code = #{competitionCode}
        ORDER BY create_time DESC
    </select>

    <!-- 根据作品编号查询作品 -->
    <select id="selectByCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM work
        WHERE code = #{code}
        LIMIT 1
    </select>

    <!-- 更新作品状态 -->
    <update id="updateStatus">
        UPDATE work
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据用户ID和赛事编号查询作品 -->
    <select id="selectByUserIdAndCompetitionCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM work
        WHERE create_user_id = #{userId} AND competition_code = #{competitionCode}
        LIMIT 1
    </select>

</mapper>
