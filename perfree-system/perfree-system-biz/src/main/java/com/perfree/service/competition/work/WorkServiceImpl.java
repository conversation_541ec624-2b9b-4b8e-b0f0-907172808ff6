package com.perfree.service.competition.work;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.perfree.controller.competition.work.vo.*;
import com.perfree.mapper.*;
import com.perfree.model.*;
import com.perfree.service.competition.competition.CompetitionService;
import com.perfree.service.competition.team.CTeamService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 作品服务实现类
 * <AUTHOR>
 */
@Service
public class WorkServiceImpl extends ServiceImpl<WorkMapper, Work> implements WorkService {

    @Resource
    private WorkMapper workMapper;

    @Resource
    private WorkParticipantMapper workParticipantMapper;

    @Resource
    private WorkInstructorMapper workInstructorMapper;

    @Resource
    private WorkFileMapper workFileMapper;

    @Resource
    private CompetitionService competitionService;

    @Resource
    private CTeamService teamService;

    @Override
    public List<WorkListRespVO> getWorkListByUserId(Integer userId) {
        List<Work> works = workMapper.findByUserId(userId);
        List<WorkListRespVO> result = new ArrayList<>();
        
        for (Work work : works) {
            WorkListRespVO vo = new WorkListRespVO();
            vo.setId(work.getId());
            vo.setCode(work.getCode());
            vo.setName(work.getName());
            vo.setCompetitionCode(work.getCompetitionCode());
            vo.setType(work.getType());
            vo.setStatus(work.getStatus());
            vo.setSubmitTime(work.getSubmitTime());
            vo.setCreateTime(work.getCreateTime());
            vo.setUpdateTime(work.getUpdateTime());
            
            // 获取赛事名称
            var competition = competitionService.findByCompetitionCode(work.getCompetitionCode());
            if (competition != null) {
                vo.setCompetitionName(competition.getName());
            }
            
            // 获取参与者和指导老师信息
            List<WorkParticipant> participants = workParticipantMapper.findByWorkId(work.getId());
            vo.setAuthors(participants.stream().map(WorkParticipant::getName).collect(Collectors.toList()));

            List<WorkInstructor> instructors = workInstructorMapper.findByWorkId(work.getId());
            vo.setInstructors(instructors.stream().map(WorkInstructor::getName).collect(Collectors.toList()));
            
            result.add(vo);
        }
        
        return result;
    }

    @Override
    public WorkDetailRespVO getWorkDetail(String id) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            return null;
        }
        
        WorkDetailRespVO vo = new WorkDetailRespVO();
        vo.setId(work.getId());
        vo.setCode(work.getCode());
        vo.setName(work.getName());
        vo.setCompetitionCode(work.getCompetitionCode());
        vo.setType(work.getType());
        vo.setAbstract_(work.getAbstract_());
        vo.setProjectPlanPath(work.getProjectPlanPath());
        vo.setProjectPlanName(work.getProjectPlanName());
        vo.setVideoLink(work.getVideoLink());
        vo.setVideoCode(work.getVideoCode());
        vo.setStatus(work.getStatus());
        vo.setSubmitTime(work.getSubmitTime());
        vo.setUserId(work.getUserId());
        vo.setTeamId(work.getTeamId());
        vo.setCreateTime(work.getCreateTime());
        vo.setUpdateTime(work.getUpdateTime());
        
        // 获取赛事名称
        var competition = competitionService.findByCompetitionCode(work.getCompetitionCode());
        if (competition != null) {
            vo.setCompetitionName(competition.getName());
        }
        
        // 获取团队名称
        if (work.getTeamId() != null) {
            var team = teamService.findById(work.getTeamId());
            if (team != null) {
                vo.setTeamName(team.getName());
            }
        }

        // 获取参与者信息
        List<WorkParticipant> participants = workParticipantMapper.findByWorkId(work.getId());
        vo.setAuthors(participants.stream().map(p -> {
            WorkDetailRespVO.ParticipantVO participantVO = new WorkDetailRespVO.ParticipantVO();
            participantVO.setId(p.getId());
            participantVO.setName(p.getName());
            participantVO.setRole(p.getRole());
            participantVO.setStudentId(p.getStudentId());
            participantVO.setPhone(p.getPhone());
            participantVO.setEmail(p.getEmail());
            return participantVO;
        }).collect(Collectors.toList()));

        // 获取指导老师信息
        List<WorkInstructor> instructors = workInstructorMapper.findByWorkId(work.getId());
        vo.setInstructors(instructors.stream().map(i -> {
            WorkDetailRespVO.InstructorVO instructorVO = new WorkDetailRespVO.InstructorVO();
            instructorVO.setId(i.getId());
            instructorVO.setName(i.getName());
            instructorVO.setRole(i.getRole());
            instructorVO.setUnit(i.getUnit());
            instructorVO.setContact(i.getContact());
            return instructorVO;
        }).collect(Collectors.toList()));

        // 获取文件信息
        List<WorkFile> files = workFileMapper.findByWorkId(work.getId());
        vo.setFiles(files.stream().map(f -> {
            WorkDetailRespVO.WorkFileVO fileVO = new WorkDetailRespVO.WorkFileVO();
            fileVO.setId(f.getId());
            fileVO.setName(f.getName());
            fileVO.setPath(f.getPath());
            fileVO.setUrl(f.getUrl());
            fileVO.setType(f.getType());
            fileVO.setSize(f.getSize());
            fileVO.setUploadTime(f.getUploadTime());
            return fileVO;
        }).collect(Collectors.toList()));
        
        return vo;
    }

    @Override
    @Transactional
    public boolean updateWork(String id, WorkUpdateReqVO reqVO) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            return false;
        }
        
        work.setName(reqVO.getName());
        work.setType(reqVO.getType());
        work.setAbstract_(reqVO.getAbstract_());
        work.setVideoLink(reqVO.getVideoLink());
        work.setVideoCode(reqVO.getVideoCode());
        
        // TODO: 处理项目计划书文件上传
        if (reqVO.getProjectPlan() != null) {
            // 文件上传逻辑
        }
        
        return workMapper.updateById(work) > 0;
    }

    @Override
    @Transactional
    public boolean deleteWork(String id) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            return false;
        }
        
        // 只有草稿状态的作品才能删除
        if (!"draft".equals(work.getStatus())) {
            return false;
        }
        
        return workMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean submitWork(String id) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            return false;
        }
        
        work.setStatus("submitted");
        work.setSubmitTime(LocalDateTime.now());
        
        return workMapper.updateById(work) > 0;
    }

    @Override
    @Transactional
    public boolean exitCompetition(String id) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            return false;
        }
        
        // 只有草稿状态的作品才能退出比赛
        if (!"draft".equals(work.getStatus())) {
            return false;
        }
        
        return workMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean updateParticipants(String id, WorkParticipantsUpdateReqVO reqVO) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            return false;
        }
        
        // TODO: 实现参与者更新逻辑
        return true;
    }

    @Override
    @Transactional
    public boolean updateInstructors(String id, WorkInstructorsUpdateReqVO reqVO) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            return false;
        }
        
        // TODO: 实现指导老师更新逻辑
        return true;
    }

    @Override
    public byte[] downloadWorkFiles(String id) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            return null;
        }
        
        // TODO: 实现文件下载逻辑
        return new byte[0];
    }

    @Override
    @Transactional
    public Work createWork(Work work) {
        if (work.getCode() == null) {
            work.setCode(generateWorkCode());
        }
        if (work.getStatus() == null) {
            work.setStatus("draft");
        }
        
        workMapper.insert(work);
        return work;
    }

    @Override
    public Work getWorkByUserIdAndCompetitionCode(Integer userId, String competitionCode) {
        return workMapper.findByUserIdAndCompetitionCode(userId, competitionCode);
    }

    @Override
    public Work getWorkByTeamIdAndCompetitionCode(String teamId, String competitionCode) {
        return workMapper.findByTeamIdAndCompetitionCode(teamId, competitionCode);
    }

    /**
     * 生成作品编号
     * @return 作品编号
     */
    private String generateWorkCode() {
        return "WORK-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
