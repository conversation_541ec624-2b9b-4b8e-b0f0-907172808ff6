package com.perfree.service.competition.work;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.perfree.commons.common.PageResult;
import com.perfree.commons.exception.ServiceException;
import com.perfree.controller.competition.work.vo.WorkAddReqVO;
import com.perfree.controller.competition.work.vo.WorkPageReqVO;
import com.perfree.controller.competition.work.vo.WorkUpdateReqVO;
import com.perfree.convert.competition.work.WorkConvert;
import com.perfree.enums.ErrorCode;
import com.perfree.mapper.WorkMapper;
import com.perfree.model.Work;
import com.perfree.security.SecurityFrameworkUtils;
import com.perfree.security.vo.LoginUserVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品服务实现类
 */
@Service
public class WorkServiceImpl extends ServiceImpl<WorkMapper, Work> implements WorkService {

    @Resource
    private WorkMapper workMapper;

    @Override
    public PageResult<Work> getWorkPage(WorkPageReqVO pageVO) {
        return workMapper.selectWorkPage(pageVO);
    }

    @Override
    public List<Work> getWorksByUserId(Integer userId) {
        return workMapper.selectByUserId(userId);
    }

    @Override
    public Work getWorkDetail(String id) {
        Work work = workMapper.selectById(id);
        if (work == null) {
            throw new ServiceException(ErrorCode.NOT_FOUND, "作品不存在");
        }
        return work;
    }

    @Override
    @Transactional
    public Work addWork(WorkAddReqVO reqVO) {
        // 检查用户是否已经在该赛事中有作品
        LoginUserVO loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException(ErrorCode.USER_NOT_LOGIN);
        }
        
        Work existingWork = workMapper.selectByUserIdAndCompetitionCode(loginUser.getId(), reqVO.getCompetitionCode());
        if (existingWork != null) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "您已经在该赛事中提交了作品");
        }

        Work work = WorkConvert.INSTANCE.convertAddReqVO(reqVO);
        work.setId(IdUtil.simpleUUID());
        work.setCode(generateWorkCode());
        work.setStatus(0); // 草稿状态
        work.setCreateUserId(loginUser.getId());
        
        workMapper.insert(work);
        return work;
    }

    @Override
    @Transactional
    public Work updateWork(WorkUpdateReqVO reqVO) {
        Work existingWork = getWorkDetail(reqVO.getId());
        
        // 检查权限
        checkWorkPermission(existingWork);
        
        // 已提交的作品不能修改
        if (existingWork.getStatus() != null && existingWork.getStatus() > 0) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "已提交的作品不能修改");
        }

        Work work = WorkConvert.INSTANCE.convertUpdateReqVO(reqVO);
        workMapper.updateById(work);
        return workMapper.selectById(reqVO.getId());
    }

    @Override
    @Transactional
    public Boolean updateWorkInfo(String id, WorkUpdateReqVO reqVO) {
        Work existingWork = getWorkDetail(id);
        checkWorkPermission(existingWork);
        
        if (existingWork.getStatus() != null && existingWork.getStatus() > 0) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "已提交的作品不能修改");
        }

        Work work = new Work();
        work.setId(id);
        work.setName(reqVO.getName());
        work.setType(reqVO.getType());
        work.setAbstract(reqVO.getAbstract());
        work.setProjectPlan(reqVO.getProjectPlan());
        work.setVideoLink(reqVO.getVideoLink());
        work.setVideoCode(reqVO.getVideoCode());
        
        return workMapper.updateById(work) > 0;
    }

    @Override
    @Transactional
    public Boolean updateParticipants(String id, List<Work.WorkParticipant> participants) {
        Work existingWork = getWorkDetail(id);
        checkWorkPermission(existingWork);
        
        if (existingWork.getStatus() != null && existingWork.getStatus() > 0) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "已提交的作品不能修改");
        }

        Work work = new Work();
        work.setId(id);
        work.setParticipants(participants);
        
        return workMapper.updateById(work) > 0;
    }

    @Override
    @Transactional
    public Boolean updateInstructors(String id, List<Work.WorkInstructor> instructors) {
        Work existingWork = getWorkDetail(id);
        checkWorkPermission(existingWork);
        
        if (existingWork.getStatus() != null && existingWork.getStatus() > 0) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "已提交的作品不能修改");
        }

        Work work = new Work();
        work.setId(id);
        work.setInstructors(instructors);
        
        return workMapper.updateById(work) > 0;
    }

    @Override
    @Transactional
    public Boolean deleteWork(String id) {
        Work existingWork = getWorkDetail(id);
        checkWorkPermission(existingWork);
        
        if (existingWork.getStatus() != null && existingWork.getStatus() > 0) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "已提交的作品不能删除");
        }
        
        return workMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public Boolean submitWork(String id) {
        Work existingWork = getWorkDetail(id);
        checkWorkPermission(existingWork);
        
        if (existingWork.getStatus() != null && existingWork.getStatus() > 0) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "作品已经提交过了");
        }
        
        // 检查必要信息是否完整
        validateWorkForSubmit(existingWork);
        
        Work work = new Work();
        work.setId(id);
        work.setStatus(1); // 已提交
        work.setSubmitTime(LocalDateTime.now());
        
        return workMapper.updateById(work) > 0;
    }

    @Override
    @Transactional
    public Boolean exitCompetition(String id) {
        Work existingWork = getWorkDetail(id);
        checkWorkPermission(existingWork);
        
        return workMapper.deleteById(id) > 0;
    }

    @Override
    public Work getWorkByCode(String code) {
        return workMapper.selectByCode(code);
    }

    @Override
    public Work getWorkByUserIdAndCompetitionCode(Integer userId, String competitionCode) {
        return workMapper.selectByUserIdAndCompetitionCode(userId, competitionCode);
    }

    /**
     * 检查作品权限
     */
    private void checkWorkPermission(Work work) {
        LoginUserVO loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException(ErrorCode.USER_NOT_LOGIN);
        }
        
        if (!work.getCreateUserId().equals(loginUser.getId())) {
            throw new ServiceException(ErrorCode.FORBIDDEN, "无权限操作该作品");
        }
    }

    /**
     * 验证作品提交前的必要信息
     */
    private void validateWorkForSubmit(Work work) {
        if (work.getName() == null || work.getName().trim().isEmpty()) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "作品名称不能为空");
        }
        
        if (work.getParticipants() == null || work.getParticipants().isEmpty()) {
            throw new ServiceException(ErrorCode.BUSINESS_ERROR, "参与者信息不能为空");
        }
    }

    /**
     * 生成作品编号
     */
    private String generateWorkCode() {
        return "WORK" + System.currentTimeMillis();
    }
}
