package com.perfree.service.competition.work;

import com.baomidou.mybatisplus.extension.service.IService;
import com.perfree.controller.competition.work.vo.*;
import com.perfree.model.Work;

import java.util.List;

/**
 * 作品服务接口
 * <AUTHOR>
 */
public interface WorkService extends IService<Work> {

    /**
     * 根据用户ID获取作品列表
     * @param userId 用户ID
     * @return 作品列表
     */
    List<WorkListRespVO> getWorkListByUserId(Integer userId);

    /**
     * 根据作品ID获取作品详情
     * @param id 作品ID
     * @return 作品详情
     */
    WorkDetailRespVO getWorkDetail(String id);

    /**
     * 更新作品基本信息
     * @param id 作品ID
     * @param reqVO 更新请求
     * @return 是否成功
     */
    boolean updateWork(String id, WorkUpdateReqVO reqVO);

    /**
     * 删除作品
     * @param id 作品ID
     * @return 是否成功
     */
    boolean deleteWork(String id);

    /**
     * 提交作品
     * @param id 作品ID
     * @return 是否成功
     */
    boolean submitWork(String id);

    /**
     * 退出比赛
     * @param id 作品ID
     * @return 是否成功
     */
    boolean exitCompetition(String id);

    /**
     * 更新作品参与者
     * @param id 作品ID
     * @param reqVO 参与者更新请求
     * @return 是否成功
     */
    boolean updateParticipants(String id, WorkParticipantsUpdateReqVO reqVO);

    /**
     * 更新作品指导老师
     * @param id 作品ID
     * @param reqVO 指导老师更新请求
     * @return 是否成功
     */
    boolean updateInstructors(String id, WorkInstructorsUpdateReqVO reqVO);

    /**
     * 下载作品文件
     * @param id 作品ID
     * @return 文件字节数组
     */
    byte[] downloadWorkFiles(String id);

    /**
     * 创建作品
     * @param work 作品信息
     * @return 创建的作品
     */
    Work createWork(Work work);

    /**
     * 根据用户ID和赛事编号获取作品
     * @param userId 用户ID
     * @param competitionCode 赛事编号
     * @return 作品
     */
    Work getWorkByUserIdAndCompetitionCode(Integer userId, String competitionCode);

    /**
     * 根据团队ID和赛事编号获取作品
     * @param teamId 团队ID
     * @param competitionCode 赛事编号
     * @return 作品
     */
    Work getWorkByTeamIdAndCompetitionCode(String teamId, String competitionCode);
}
