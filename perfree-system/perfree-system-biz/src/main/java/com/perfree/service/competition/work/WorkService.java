package com.perfree.service.competition.work;

import com.baomidou.mybatisplus.extension.service.IService;
import com.perfree.commons.common.PageResult;
import com.perfree.controller.competition.work.vo.WorkAddReqVO;
import com.perfree.controller.competition.work.vo.WorkPageReqVO;
import com.perfree.controller.competition.work.vo.WorkUpdateReqVO;
import com.perfree.model.Work;

import java.util.List;

/**
 * 作品服务接口
 */
public interface WorkService extends IService<Work> {

    /**
     * 分页查询作品列表
     * @param pageVO 分页查询参数
     * @return 分页结果
     */
    PageResult<Work> getWorkPage(WorkPageReqVO pageVO);

    /**
     * 根据用户ID查询作品列表
     * @param userId 用户ID
     * @return 作品列表
     */
    List<Work> getWorksByUserId(Integer userId);

    /**
     * 根据作品ID查询作品详情
     * @param id 作品ID
     * @return 作品详情
     */
    Work getWorkDetail(String id);

    /**
     * 新增作品
     * @param reqVO 新增请求参数
     * @return 作品信息
     */
    Work addWork(WorkAddReqVO reqVO);

    /**
     * 更新作品信息
     * @param reqVO 更新请求参数
     * @return 作品信息
     */
    Work updateWork(WorkUpdateReqVO reqVO);

    /**
     * 更新作品基本信息
     * @param id 作品ID
     * @param reqVO 更新请求参数
     * @return 是否成功
     */
    Boolean updateWorkInfo(String id, WorkUpdateReqVO reqVO);

    /**
     * 更新作品参与者信息
     * @param id 作品ID
     * @param participants 参与者列表
     * @return 是否成功
     */
    Boolean updateParticipants(String id, List<Work.WorkParticipant> participants);

    /**
     * 更新作品指导老师信息
     * @param id 作品ID
     * @param instructors 指导老师列表
     * @return 是否成功
     */
    Boolean updateInstructors(String id, List<Work.WorkInstructor> instructors);

    /**
     * 删除作品
     * @param id 作品ID
     * @return 是否成功
     */
    Boolean deleteWork(String id);

    /**
     * 提交作品
     * @param id 作品ID
     * @return 是否成功
     */
    Boolean submitWork(String id);

    /**
     * 退出比赛
     * @param id 作品ID
     * @return 是否成功
     */
    Boolean exitCompetition(String id);

    /**
     * 根据作品编号查询作品
     * @param code 作品编号
     * @return 作品信息
     */
    Work getWorkByCode(String code);

    /**
     * 根据用户ID和赛事编号查询作品
     * @param userId 用户ID
     * @param competitionCode 赛事编号
     * @return 作品信息
     */
    Work getWorkByUserIdAndCompetitionCode(Integer userId, String competitionCode);
}
