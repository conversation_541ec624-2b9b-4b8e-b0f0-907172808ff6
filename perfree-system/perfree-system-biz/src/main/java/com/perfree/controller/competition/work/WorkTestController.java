package com.perfree.controller.competition.work;

import com.perfree.commons.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 作品管理测试控制器
 */
@RestController
@Tag(name = "作品管理测试接口")
@RequestMapping("/api/competition/work/test")
public class WorkTestController {

    @GetMapping("/health")
    @Operation(summary = "健康检查")
    public CommonResult<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "ok");
        result.put("message", "Work module is running");
        result.put("timestamp", System.currentTimeMillis());
        return CommonResult.success(result);
    }
}
