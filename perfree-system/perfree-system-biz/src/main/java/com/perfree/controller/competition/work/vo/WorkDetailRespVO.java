package com.perfree.controller.competition.work.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品详情响应VO
 * <AUTHOR>
 */
@Data
public class WorkDetailRespVO {

    /**
     * 作品ID
     */
    private String id;

    /**
     * 作品编号
     */
    private String code;

    /**
     * 作品名称
     */
    private String name;

    /**
     * 赛事编号
     */
    private String competitionCode;

    /**
     * 赛事名称
     */
    private String competitionName;

    /**
     * 作品类型
     */
    private String type;

    /**
     * 作品简介
     */
    private String abstract_;

    /**
     * 项目计划书文件路径
     */
    private String projectPlanPath;

    /**
     * 项目计划书文件名
     */
    private String projectPlanName;

    /**
     * 视频链接
     */
    private String videoLink;

    /**
     * 视频提取码
     */
    private String videoCode;

    /**
     * 作品状态
     */
    private String status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 创建用户ID
     */
    private Integer userId;

    /**
     * 团队ID
     */
    private String teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 参与者列表
     */
    private List<ParticipantVO> authors;

    /**
     * 指导老师列表
     */
    private List<InstructorVO> instructors;

    /**
     * 作品文件列表
     */
    private List<WorkFileVO> files;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 参与者VO
     */
    @Data
    public static class ParticipantVO {
        private String id;
        private String name;
        private String role;
        private String studentId;
        private String phone;
        private String email;
    }

    /**
     * 指导老师VO
     */
    @Data
    public static class InstructorVO {
        private String id;
        private String name;
        private String role;
        private String unit;
        private String contact;
    }

    /**
     * 作品文件VO
     */
    @Data
    public static class WorkFileVO {
        private String id;
        private String name;
        private String path;
        private String url;
        private String type;
        private Long size;
        private LocalDateTime uploadTime;
    }
}
