package com.perfree.controller.competition.work.vo;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 作品参与者更新请求VO
 * <AUTHOR>
 */
@Data
public class WorkParticipantsUpdateReqVO {

    /**
     * 参与者列表
     */
    @NotEmpty(message = "参与者列表不能为空")
    @Valid
    private List<ParticipantReqVO> authors;

    /**
     * 参与者请求VO
     */
    @Data
    public static class ParticipantReqVO {
        /**
         * 参与者ID（更新时需要）
         */
        private String id;

        /**
         * 姓名
         */
        private String name;

        /**
         * 角色
         */
        private String role;

        /**
         * 学号
         */
        private String studentId;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 邮箱
         */
        private String email;
    }
}
