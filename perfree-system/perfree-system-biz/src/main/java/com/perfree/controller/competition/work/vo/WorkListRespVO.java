package com.perfree.controller.competition.work.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品列表响应VO
 * <AUTHOR>
 */
@Data
public class WorkListRespVO {

    /**
     * 作品ID
     */
    private String id;

    /**
     * 作品编号
     */
    private String code;

    /**
     * 作品名称
     */
    private String name;

    /**
     * 赛事编号
     */
    private String competitionCode;

    /**
     * 赛事名称
     */
    private String competitionName;

    /**
     * 作品类型
     */
    private String type;

    /**
     * 作品状态
     */
    private String status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 参与者姓名列表
     */
    private List<String> authors;

    /**
     * 指导老师姓名列表
     */
    private List<String> instructors;
}
