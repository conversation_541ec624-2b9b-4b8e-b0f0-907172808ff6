package com.perfree.controller.competition.work.vo;

import com.perfree.commons.common.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 作品分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkPageReqVO extends PageParam {

    /**
     * 作品名称
     */
    private String name;

    /**
     * 赛事编号
     */
    private String competitionCode;

    /**
     * 作品类型
     */
    private String type;

    /**
     * 作品状态
     */
    private Integer status;

    /**
     * 创建用户ID
     */
    private Integer createUserId;
}
