package com.perfree.controller.competition.work.vo;

import jakarta.validation.Valid;
import lombok.Data;

import java.util.List;

/**
 * 作品指导老师更新请求VO
 * <AUTHOR>
 */
@Data
public class WorkInstructorsUpdateReqVO {

    /**
     * 指导老师列表
     */
    @Valid
    private List<InstructorReqVO> instructors;

    /**
     * 指导老师请求VO
     */
    @Data
    public static class InstructorReqVO {
        /**
         * 指导老师ID（更新时需要）
         */
        private String id;

        /**
         * 姓名
         */
        private String name;

        /**
         * 角色
         */
        private String role;

        /**
         * 单位
         */
        private String unit;

        /**
         * 联系方式
         */
        private String contact;
    }
}
