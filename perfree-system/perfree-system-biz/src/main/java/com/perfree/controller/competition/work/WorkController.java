package com.perfree.controller.competition.work;

import com.perfree.commons.common.CommonResult;
import com.perfree.commons.common.PageResult;
import com.perfree.controller.competition.work.vo.*;
import com.perfree.convert.competition.work.WorkConvert;
import com.perfree.model.Work;
import com.perfree.security.SecurityFrameworkUtils;
import com.perfree.security.vo.LoginUserVO;
import com.perfree.service.competition.work.WorkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.perfree.commons.common.CommonResult.success;

/**
 * 作品管理控制器
 */
@RestController
@Tag(name = "作品管理相关接口")
@RequestMapping("/api/competition/work")
public class WorkController {

    @Resource
    private WorkService workService;

    @PostMapping("/page")
    @Operation(summary = "作品分页列表")
    public CommonResult<PageResult<WorkRespVO>> page(@RequestBody WorkPageReqVO pageVO) {
        PageResult<Work> workPageResult = workService.getWorkPage(pageVO);
        return success(WorkConvert.INSTANCE.convertPageResultVO(workPageResult));
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "根据用户ID获取作品列表")
    public CommonResult<List<WorkRespVO>> getWorksByUserId(
            @Parameter(description = "用户ID") @PathVariable Integer userId) {
        List<Work> works = workService.getWorksByUserId(userId);
        return success(WorkConvert.INSTANCE.convertRespListVO(works));
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取作品详情")
    public CommonResult<WorkRespVO> getWorkDetail(
            @Parameter(description = "作品ID") @PathVariable String id) {
        Work work = workService.getWorkDetail(id);
        return success(WorkConvert.INSTANCE.convertRespVO(work));
    }

    @PostMapping("/add")
    @Operation(summary = "新增作品")
    public CommonResult<WorkRespVO> addWork(@RequestBody @Valid WorkAddReqVO reqVO) {
        Work work = workService.addWork(reqVO);
        return success(WorkConvert.INSTANCE.convertRespVO(work));
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "更新作品")
    public CommonResult<WorkRespVO> updateWork(
            @Parameter(description = "作品ID") @PathVariable String id,
            @RequestBody @Valid WorkUpdateReqVO reqVO) {
        reqVO.setId(id);
        Work work = workService.updateWork(reqVO);
        return success(WorkConvert.INSTANCE.convertRespVO(work));
    }

    @PutMapping("/info/{id}")
    @Operation(summary = "更新作品基本信息")
    public CommonResult<Boolean> updateWorkInfo(
            @Parameter(description = "作品ID") @PathVariable String id,
            @RequestBody @Valid WorkUpdateReqVO reqVO) {
        return success(workService.updateWorkInfo(id, reqVO));
    }

    @PutMapping("/participants/{id}")
    @Operation(summary = "更新作品参与者信息")
    public CommonResult<Boolean> updateParticipants(
            @Parameter(description = "作品ID") @PathVariable String id,
            @RequestBody List<Work.WorkParticipant> participants) {
        return success(workService.updateParticipants(id, participants));
    }

    @PutMapping("/instructors/{id}")
    @Operation(summary = "更新作品指导老师信息")
    public CommonResult<Boolean> updateInstructors(
            @Parameter(description = "作品ID") @PathVariable String id,
            @RequestBody List<Work.WorkInstructor> instructors) {
        return success(workService.updateInstructors(id, instructors));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除作品")
    public CommonResult<Boolean> deleteWork(
            @Parameter(description = "作品ID") @PathVariable String id) {
        return success(workService.deleteWork(id));
    }

    @PostMapping("/submit/{id}")
    @Operation(summary = "提交作品")
    public CommonResult<Boolean> submitWork(
            @Parameter(description = "作品ID") @PathVariable String id) {
        return success(workService.submitWork(id));
    }

    @PostMapping("/exit/{id}")
    @Operation(summary = "退出比赛")
    public CommonResult<Boolean> exitCompetition(
            @Parameter(description = "作品ID") @PathVariable String id) {
        return success(workService.exitCompetition(id));
    }

    @GetMapping("/download/{id}")
    @Operation(summary = "下载作品文件")
    public CommonResult<String> downloadWorkFiles(
            @Parameter(description = "作品ID") @PathVariable String id) {
        // TODO: 实现文件下载逻辑
        return success("download/" + id);
    }

    @GetMapping("/my")
    @Operation(summary = "获取当前用户的作品列表")
    public CommonResult<List<WorkRespVO>> getMyWorks() {
        LoginUserVO loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            return CommonResult.error(401, "用户未登录");
        }
        List<Work> works = workService.getWorksByUserId(loginUser.getId());
        return success(WorkConvert.INSTANCE.convertRespListVO(works));
    }
}
