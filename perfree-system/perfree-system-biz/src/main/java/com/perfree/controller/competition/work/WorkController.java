package com.perfree.controller.competition.work;

import com.perfree.commons.common.CommonResult;
import com.perfree.controller.competition.work.vo.*;
import com.perfree.security.util.SecurityFrameworkUtils;
import com.perfree.security.vo.LoginUserVO;
import com.perfree.service.competition.work.WorkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 作品管理控制器
 * <AUTHOR>
 */
@RestController
@Tag(name = "作品管理相关接口")
@RequestMapping("/api/competition/work")
public class WorkController {

    @Resource
    private WorkService workService;

    @GetMapping("/user/{userId}")
    @Operation(summary = "根据用户ID获取作品列表")
    public CommonResult<List<WorkListRespVO>> getWorkListByUserId(@PathVariable("userId") Integer userId) {
        // 权限检查：只能查看自己的作品或管理员可以查看所有作品
        LoginUserVO loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            return CommonResult.error(401, "用户未登录");
        }
        
        // 简单权限检查，实际应该更严格
        if (!loginUser.getId().equals(userId) && !isAdmin(loginUser)) {
            return CommonResult.error(403, "无权限访问");
        }
        
        List<WorkListRespVO> result = workService.getWorkListByUserId(userId);
        return CommonResult.success(result);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取作品详情")
    public CommonResult<WorkDetailRespVO> getWorkDetail(@PathVariable("id") String id) {
        WorkDetailRespVO result = workService.getWorkDetail(id);
        if (result == null) {
            return CommonResult.error(404, "作品不存在");
        }
        return CommonResult.success(result);
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "更新作品基本信息")
    public CommonResult<Boolean> updateWork(@PathVariable("id") String id, 
                                          @RequestBody @Valid WorkUpdateReqVO reqVO) {
        boolean result = workService.updateWork(id, reqVO);
        if (!result) {
            return CommonResult.error(500, "更新失败");
        }
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除作品")
    public CommonResult<Boolean> deleteWork(@PathVariable("id") String id) {
        boolean result = workService.deleteWork(id);
        if (!result) {
            return CommonResult.error(500, "删除失败");
        }
        return CommonResult.success(true);
    }

    @PostMapping("/submit/{id}")
    @Operation(summary = "提交作品")
    public CommonResult<Boolean> submitWork(@PathVariable("id") String id) {
        boolean result = workService.submitWork(id);
        if (!result) {
            return CommonResult.error(500, "提交失败");
        }
        return CommonResult.success(true);
    }

    @PostMapping("/exit/{id}")
    @Operation(summary = "退出比赛")
    public CommonResult<Boolean> exitCompetition(@PathVariable("id") String id) {
        boolean result = workService.exitCompetition(id);
        if (!result) {
            return CommonResult.error(500, "退出失败");
        }
        return CommonResult.success(true);
    }

    @PutMapping("/participants/{id}")
    @Operation(summary = "更新作品参与者")
    public CommonResult<Boolean> updateParticipants(@PathVariable("id") String id,
                                                   @RequestBody @Valid WorkParticipantsUpdateReqVO reqVO) {
        boolean result = workService.updateParticipants(id, reqVO);
        if (!result) {
            return CommonResult.error(500, "更新参与者失败");
        }
        return CommonResult.success(true);
    }

    @PutMapping("/info/{id}")
    @Operation(summary = "更新作品信息")
    public CommonResult<Boolean> updateWorkInfo(@PathVariable("id") String id,
                                               @RequestBody @Valid WorkUpdateReqVO reqVO) {
        boolean result = workService.updateWork(id, reqVO);
        if (!result) {
            return CommonResult.error(500, "更新作品信息失败");
        }
        return CommonResult.success(true);
    }

    @PutMapping("/instructors/{id}")
    @Operation(summary = "更新作品指导老师")
    public CommonResult<Boolean> updateInstructors(@PathVariable("id") String id,
                                                  @RequestBody @Valid WorkInstructorsUpdateReqVO reqVO) {
        boolean result = workService.updateInstructors(id, reqVO);
        if (!result) {
            return CommonResult.error(500, "更新指导老师失败");
        }
        return CommonResult.success(true);
    }

    @GetMapping("/download/{id}")
    @Operation(summary = "下载作品文件")
    public ResponseEntity<byte[]> downloadWorkFiles(@PathVariable("id") String id) {
        byte[] fileData = workService.downloadWorkFiles(id);
        if (fileData == null || fileData.length == 0) {
            return ResponseEntity.notFound().build();
        }
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "work-files-" + id + ".zip");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(fileData);
    }

    /**
     * 检查是否为管理员
     * @param loginUser 登录用户
     * @return 是否为管理员
     */
    private boolean isAdmin(LoginUserVO loginUser) {
        // TODO: 实现管理员权限检查逻辑
        return false;
    }
}
