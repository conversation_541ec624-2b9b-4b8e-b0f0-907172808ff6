package com.perfree.controller.competition.work.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 作品更新请求VO
 * <AUTHOR>
 */
@Data
public class WorkUpdateReqVO {

    /**
     * 作品名称
     */
    @NotBlank(message = "作品名称不能为空")
    @Size(max = 128, message = "作品名称长度不能超过128个字符")
    private String name;

    /**
     * 作品类型
     */
    @NotBlank(message = "作品类型不能为空")
    @Size(max = 32, message = "作品类型长度不能超过32个字符")
    private String type;

    /**
     * 作品简介
     */
    @NotBlank(message = "作品简介不能为空")
    @Size(max = 1000, message = "作品简介长度不能超过1000个字符")
    private String abstract_;

    /**
     * 项目计划书文件
     */
    private MultipartFile projectPlan;

    /**
     * 视频链接
     */
    @Size(max = 500, message = "视频链接长度不能超过500个字符")
    private String videoLink;

    /**
     * 视频提取码
     */
    @Size(max = 20, message = "视频提取码长度不能超过20个字符")
    private String videoCode;
}
