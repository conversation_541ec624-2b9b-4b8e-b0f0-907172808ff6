package com.perfree.controller.competition.work.vo;

import com.perfree.model.Work;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 作品更新请求VO
 */
@Data
public class WorkUpdateReqVO {

    /**
     * 主键
     */
    @NotBlank(message = "作品ID不能为空")
    private String id;

    /**
     * 作品名称
     */
    @NotBlank(message = "作品名称不能为空")
    @Size(max = 128, message = "作品名称长度不能超过128个字符")
    private String name;

    /**
     * 作品类型
     */
    private String type;

    /**
     * 作品摘要
     */
    @Size(max = 1000, message = "作品摘要长度不能超过1000个字符")
    private String abstract;

    /**
     * 项目计划书路径
     */
    private String projectPlan;

    /**
     * 视频链接
     */
    private String videoLink;

    /**
     * 视频提取码
     */
    private String videoCode;

    /**
     * 参与者信息
     */
    private List<Work.WorkParticipant> participants;

    /**
     * 指导老师信息
     */
    private List<Work.WorkInstructor> instructors;

    /**
     * 作品文件列表
     */
    private List<Work.WorkFile> files;
}
