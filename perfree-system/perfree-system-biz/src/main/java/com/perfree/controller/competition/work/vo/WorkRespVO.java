package com.perfree.controller.competition.work.vo;

import com.perfree.model.Work;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品响应VO
 */
@Data
public class WorkRespVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 作品编号
     */
    private String code;

    /**
     * 作品名称
     */
    private String name;

    /**
     * 赛事编号
     */
    private String competitionCode;

    /**
     * 赛事名称
     */
    private String competitionName;

    /**
     * 作品类型
     */
    private String type;

    /**
     * 作品摘要
     */
    private String abstract;

    /**
     * 项目计划书路径
     */
    private String projectPlan;

    /**
     * 视频链接
     */
    private String videoLink;

    /**
     * 视频提取码
     */
    private String videoCode;

    /**
     * 作品状态：0-草稿，1-已提交，2-审核中，3-审核通过，4-审核不通过
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 参与者信息
     */
    private List<Work.WorkParticipant> participants;

    /**
     * 指导老师信息
     */
    private List<Work.WorkInstructor> instructors;

    /**
     * 作品文件列表
     */
    private List<Work.WorkFile> files;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
