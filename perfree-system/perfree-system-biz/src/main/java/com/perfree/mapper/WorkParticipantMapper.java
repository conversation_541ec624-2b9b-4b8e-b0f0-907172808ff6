package com.perfree.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.perfree.commons.mapper.BaseMapperX;
import com.perfree.model.WorkParticipant;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 作品参与者Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface WorkParticipantMapper extends BaseMapperX<WorkParticipant> {

    /**
     * 根据作品ID查询参与者列表
     * @param workId 作品ID
     * @return 参与者列表
     */
    default List<WorkParticipant> findByWorkId(String workId) {
        return selectList(new LambdaQueryWrapper<WorkParticipant>()
                .eq(WorkParticipant::getWorkId, workId)
                .orderByAsc(WorkParticipant::getCreateTime)
        );
    }

    /**
     * 根据作品ID删除参与者
     * @param workId 作品ID
     * @return 删除数量
     */
    default int deleteByWorkId(String workId) {
        return delete(new LambdaQueryWrapper<WorkParticipant>()
                .eq(WorkParticipant::getWorkId, workId)
        );
    }
}
