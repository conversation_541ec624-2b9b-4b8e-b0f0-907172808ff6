package com.perfree.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.perfree.commons.mapper.BaseMapperX;
import com.perfree.model.WorkInstructor;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 作品指导老师Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface WorkInstructorMapper extends BaseMapperX<WorkInstructor> {

    /**
     * 根据作品ID查询指导老师列表
     * @param workId 作品ID
     * @return 指导老师列表
     */
    default List<WorkInstructor> findByWorkId(String workId) {
        return selectList(new LambdaQueryWrapper<WorkInstructor>()
                .eq(WorkInstructor::getWorkId, workId)
                .orderByAsc(WorkInstructor::getCreateTime)
        );
    }

    /**
     * 根据作品ID删除指导老师
     * @param workId 作品ID
     * @return 删除数量
     */
    default int deleteByWorkId(String workId) {
        return delete(new LambdaQueryWrapper<WorkInstructor>()
                .eq(WorkInstructor::getWorkId, workId)
        );
    }
}
