package com.perfree.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.perfree.commons.mapper.BaseMapperX;
import com.perfree.model.WorkFile;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 作品文件Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface WorkFileMapper extends BaseMapperX<WorkFile> {

    /**
     * 根据作品ID查询文件列表
     * @param workId 作品ID
     * @return 文件列表
     */
    default List<WorkFile> findByWorkId(String workId) {
        return selectList(new LambdaQueryWrapper<WorkFile>()
                .eq(WorkFile::getWorkId, workId)
                .orderByAsc(WorkFile::getUploadTime)
        );
    }

    /**
     * 根据作品ID删除文件
     * @param workId 作品ID
     * @return 删除数量
     */
    default int deleteByWorkId(String workId) {
        return delete(new LambdaQueryWrapper<WorkFile>()
                .eq(WorkFile::getWorkId, workId)
        );
    }
}
