package com.perfree.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.perfree.commons.common.PageResult;
import com.perfree.commons.mapper.BaseMapperX;
import com.perfree.model.Work;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 作品Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface WorkMapper extends BaseMapperX<Work> {

    /**
     * 根据用户ID查询作品列表
     * @param userId 用户ID
     * @return 作品列表
     */
    default List<Work> findByUserId(Integer userId) {
        return selectList(new LambdaQueryWrapper<Work>()
                .eq(Work::getUserId, userId)
                .orderByDesc(Work::getCreateTime)
        );
    }

    /**
     * 根据团队ID查询作品列表
     * @param teamId 团队ID
     * @return 作品列表
     */
    default List<Work> findByTeamId(String teamId) {
        return selectList(new LambdaQueryWrapper<Work>()
                .eq(Work::getTeamId, teamId)
                .orderByDesc(Work::getCreateTime)
        );
    }

    /**
     * 根据赛事编号查询作品列表
     * @param competitionCode 赛事编号
     * @return 作品列表
     */
    default List<Work> findByCompetitionCode(String competitionCode) {
        return selectList(new LambdaQueryWrapper<Work>()
                .eq(Work::getCompetitionCode, competitionCode)
                .orderByDesc(Work::getCreateTime)
        );
    }

    /**
     * 根据用户ID和赛事编号查询作品
     * @param userId 用户ID
     * @param competitionCode 赛事编号
     * @return 作品
     */
    default Work findByUserIdAndCompetitionCode(Integer userId, String competitionCode) {
        return selectOne(new LambdaQueryWrapper<Work>()
                .eq(Work::getUserId, userId)
                .eq(Work::getCompetitionCode, competitionCode)
        );
    }

    /**
     * 根据团队ID和赛事编号查询作品
     * @param teamId 团队ID
     * @param competitionCode 赛事编号
     * @return 作品
     */
    default Work findByTeamIdAndCompetitionCode(String teamId, String competitionCode) {
        return selectOne(new LambdaQueryWrapper<Work>()
                .eq(Work::getTeamId, teamId)
                .eq(Work::getCompetitionCode, competitionCode)
        );
    }

    /**
     * 根据作品编号查询作品
     * @param code 作品编号
     * @return 作品
     */
    default Work findByCode(String code) {
        return selectOne(new LambdaQueryWrapper<Work>()
                .eq(Work::getCode, code)
        );
    }

    /**
     * 根据状态查询作品列表
     * @param status 状态
     * @return 作品列表
     */
    default List<Work> findByStatus(String status) {
        return selectList(new LambdaQueryWrapper<Work>()
                .eq(Work::getStatus, status)
                .orderByDesc(Work::getCreateTime)
        );
    }
}
