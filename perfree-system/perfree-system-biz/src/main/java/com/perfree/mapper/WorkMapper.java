package com.perfree.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.perfree.commons.common.PageResult;
import com.perfree.controller.competition.work.vo.WorkPageReqVO;
import com.perfree.model.Work;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 作品Mapper接口
 */
@Mapper
public interface WorkMapper extends BaseMapper<Work> {

    /**
     * 分页查询作品列表
     * @param pageVO 分页查询参数
     * @return 分页结果
     */
    PageResult<Work> selectWorkPage(WorkPageReqVO pageVO);

    /**
     * 根据用户ID查询作品列表
     * @param userId 用户ID
     * @return 作品列表
     */
    List<Work> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据赛事编号查询作品列表
     * @param competitionCode 赛事编号
     * @return 作品列表
     */
    List<Work> selectByCompetitionCode(@Param("competitionCode") String competitionCode);

    /**
     * 根据作品编号查询作品
     * @param code 作品编号
     * @return 作品信息
     */
    Work selectByCode(@Param("code") String code);

    /**
     * 更新作品状态
     * @param id 作品ID
     * @param status 状态
     * @return 更新行数
     */
    int updateStatus(@Param("id") String id, @Param("status") Integer status);

    /**
     * 根据用户ID和赛事编号查询作品
     * @param userId 用户ID
     * @param competitionCode 赛事编号
     * @return 作品信息
     */
    Work selectByUserIdAndCompetitionCode(@Param("userId") Integer userId, @Param("competitionCode") String competitionCode);
}
