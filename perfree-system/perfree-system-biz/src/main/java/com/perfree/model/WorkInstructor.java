package com.perfree.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 作品指导老师实体类
 * <AUTHOR>
 */
@Data
@TableName("work_instructor")
@NoArgsConstructor
@AllArgsConstructor
public class WorkInstructor implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 作品ID
     */
    private String workId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 角色：instructor-指导老师，advisor-顾问
     */
    private String role;

    /**
     * 单位
     */
    private String unit;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}
