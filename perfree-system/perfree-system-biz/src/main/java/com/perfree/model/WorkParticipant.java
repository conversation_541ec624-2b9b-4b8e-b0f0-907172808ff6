package com.perfree.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 作品参与者实体类
 * <AUTHOR>
 */
@Data
@TableName("work_participant")
@NoArgsConstructor
@AllArgsConstructor
public class WorkParticipant implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 作品ID
     */
    private String workId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 角色：leader-队长，participant-队员
     */
    private String role;

    /**
     * 学号
     */
    private String studentId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}
