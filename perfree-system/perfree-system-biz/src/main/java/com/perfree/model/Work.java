package com.perfree.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 作品实体类
 * <AUTHOR>
 */
@Data
@TableName("work")
@NoArgsConstructor
@AllArgsConstructor
public class Work implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 作品编号
     */
    private String code;

    /**
     * 作品名称
     */
    private String name;

    /**
     * 赛事编号
     */
    private String competitionCode;

    /**
     * 作品类型
     */
    private String type;

    /**
     * 作品简介
     */
    private String abstract_;

    /**
     * 项目计划书文件路径
     */
    private String projectPlanPath;

    /**
     * 项目计划书文件名
     */
    private String projectPlanName;

    /**
     * 视频链接
     */
    private String videoLink;

    /**
     * 视频提取码
     */
    private String videoCode;

    /**
     * 作品状态：draft-草稿，submitted-已提交，reviewed-已审核
     */
    private String status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 创建用户ID
     */
    private Integer userId;

    /**
     * 团队ID（如果是团队作品）
     */
    private String teamId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}
