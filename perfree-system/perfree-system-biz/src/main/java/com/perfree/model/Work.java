package com.perfree.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品实体类
 */
@Data
@TableName(value = "work", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
public class Work implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 作品编号
     */
    private String code;

    /**
     * 作品名称
     */
    private String name;

    /**
     * 赛事编号
     */
    private String competitionCode;

    /**
     * 作品类型
     */
    private String type;

    /**
     * 作品摘要
     */
    private String abstract;

    /**
     * 项目计划书路径
     */
    private String projectPlan;

    /**
     * 视频链接
     */
    private String videoLink;

    /**
     * 视频提取码
     */
    private String videoCode;

    /**
     * 作品状态：0-草稿，1-已提交，2-审核中，3-审核通过，4-审核不通过
     */
    private Integer status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 参与者信息（JSON格式存储）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<WorkParticipant> participants;

    /**
     * 指导老师信息（JSON格式存储）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<WorkInstructor> instructors;

    /**
     * 作品文件列表（JSON格式存储）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<WorkFile> files;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建用户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUserId;

    /**
     * 更新用户ID
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUserId;

    /**
     * 参与者信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkParticipant implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String name;
        private String role; // member, leader
        private String studentId;
        private String phone;
        private String email;
    }

    /**
     * 指导老师信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkInstructor implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String name;
        private String role; // instructor, advisor
        private String unit;
        private String contact;
    }

    /**
     * 作品文件信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkFile implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String name;
        private String path;
        private String url;
        private String type;
        private Long size;
    }
}
