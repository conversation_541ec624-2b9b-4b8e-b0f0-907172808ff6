package com.perfree.convert.competition.work;

import com.perfree.commons.common.PageResult;
import com.perfree.controller.competition.work.vo.WorkAddReqVO;
import com.perfree.controller.competition.work.vo.WorkRespVO;
import com.perfree.controller.competition.work.vo.WorkUpdateReqVO;
import com.perfree.model.Work;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 作品转换器
 */
public class WorkConvert {

    public static final WorkConvert INSTANCE = new WorkConvert();

    private WorkConvert() {}

    /**
     * 实体转响应VO
     */
    public WorkRespVO convertRespVO(Work work) {
        if (work == null) {
            return null;
        }

        WorkRespVO respVO = new WorkRespVO();
        BeanUtils.copyProperties(work, respVO);
        respVO.setStatusDesc(getStatusDesc(work.getStatus()));
        // competitionName 需要单独设置
        return respVO;
    }

    /**
     * 实体列表转响应VO列表
     */
    public List<WorkRespVO> convertRespListVO(List<Work> workList) {
        if (workList == null) {
            return null;
        }

        List<WorkRespVO> respList = new ArrayList<>();
        for (Work work : workList) {
            respList.add(convertRespVO(work));
        }
        return respList;
    }

    /**
     * 分页结果转换
     */
    public PageResult<WorkRespVO> convertPageResultVO(PageResult<Work> pageResult) {
        if (pageResult == null) {
            return null;
        }

        PageResult<WorkRespVO> result = new PageResult<>();
        result.setList(convertRespListVO(pageResult.getList()));
        result.setTotal(pageResult.getTotal());
        return result;
    }

    /**
     * 新增请求VO转实体
     */
    public Work convertAddReqVO(WorkAddReqVO reqVO) {
        if (reqVO == null) {
            return null;
        }

        Work work = new Work();
        BeanUtils.copyProperties(reqVO, work);
        return work;
    }

    /**
     * 更新请求VO转实体
     */
    public Work convertUpdateReqVO(WorkUpdateReqVO reqVO) {
        if (reqVO == null) {
            return null;
        }

        Work work = new Work();
        BeanUtils.copyProperties(reqVO, work);
        return work;
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "草稿";
            case 1:
                return "已提交";
            case 2:
                return "审核中";
            case 3:
                return "审核通过";
            case 4:
                return "审核不通过";
            default:
                return "未知";
        }
    }
}
