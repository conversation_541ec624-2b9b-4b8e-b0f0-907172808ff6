package com.perfree.convert.competition.work;

import com.perfree.commons.common.PageResult;
import com.perfree.controller.competition.work.vo.WorkAddReqVO;
import com.perfree.controller.competition.work.vo.WorkRespVO;
import com.perfree.controller.competition.work.vo.WorkUpdateReqVO;
import com.perfree.model.Work;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 作品转换器
 */
@Mapper
public interface WorkConvert {

    WorkConvert INSTANCE = Mappers.getMapper(WorkConvert.class);

    /**
     * 实体转响应VO
     */
    @Mapping(target = "statusDesc", expression = "java(getStatusDesc(work.getStatus()))")
    @Mapping(target = "competitionName", ignore = true) // 需要单独设置
    WorkRespVO convertRespVO(Work work);

    /**
     * 实体列表转响应VO列表
     */
    List<WorkRespVO> convertRespListVO(List<Work> workList);

    /**
     * 分页结果转换
     */
    PageResult<WorkRespVO> convertPageResultVO(PageResult<Work> pageResult);

    /**
     * 新增请求VO转实体
     */
    Work convertAddReqVO(WorkAddReqVO reqVO);

    /**
     * 更新请求VO转实体
     */
    Work convertUpdateReqVO(WorkUpdateReqVO reqVO);

    /**
     * 获取状态描述
     */
    default String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "草稿";
            case 1:
                return "已提交";
            case 2:
                return "审核中";
            case 3:
                return "审核通过";
            case 4:
                return "审核不通过";
            default:
                return "未知";
        }
    }
}
